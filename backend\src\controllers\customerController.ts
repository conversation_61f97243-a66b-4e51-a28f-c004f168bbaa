import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import type { 
  Customer, 
  CustomerCreateInput, 
  CustomerUpdateInput, 
  PaginatedResponse 
} from '../types';

// 获取客户列表
export async function getCustomers(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { page = 1, limit = 10, search = '' } = req.query;
    
    const offset = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    let whereClause = "WHERE status != 'deleted'";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM customers ${whereClause}`;
    const countResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM customers 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...params, Number(limit), offset];
    
    const customers = await new Promise<Customer[]>((resolve, reject) => {
      db.all(dataQuery, dataParams, (err, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows as Customer[]);
      });
    });
    
    const response: PaginatedResponse<Customer> = {
      data: customers,
      total: countResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(countResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取客户列表成功',
      data: response
    });
  } catch (error) {
    console.error('获取客户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取客户列表失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 获取单个客户详情
export async function getCustomerById(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    const customer = await new Promise<Customer | undefined>((resolve, reject) => {
      db.get(
        'SELECT * FROM customers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row as Customer);
        }
      );
    });
    
    if (!customer) {
      res.status(404).json({
        success: false,
        message: '客户不存在'
      });
      return;
    }
    
    res.json({
      success: true,
      message: '获取客户详情成功',
      data: customer
    });
  } catch (error) {
    console.error('获取客户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取客户详情失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 创建客户
export async function createCustomer(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const customerData: CustomerCreateInput = req.body;
    
    // 验证必填字段
    if (!customerData.code || !customerData.name) {
      res.status(400).json({
        success: false,
        message: '客户编码和名称为必填项'
      });
      return;
    }
    
    // 检查编码是否已存在
    const existingCustomer = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM customers WHERE code = ? AND status != ?',
        [customerData.code, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (existingCustomer) {
      res.status(400).json({
        success: false,
        message: '客户编码已存在'
      });
      return;
    }
    
    // 插入新客户
    const result = await new Promise<{ lastID: number }>((resolve, reject) => {
      db.run(
        `INSERT INTO customers (code, name, contact_person, phone, address, credit_limit, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          customerData.code,
          customerData.name,
          customerData.contact_person || null,
          customerData.phone || null,
          customerData.address || null,
          customerData.credit_limit || 0
        ],
        function(err) {
          if (err) reject(err);
          else resolve({ lastID: this.lastID });
        }
      );
    });
    
    res.status(201).json({
      success: true,
      message: '创建客户成功',
      data: { id: result.lastID }
    });
  } catch (error) {
    console.error('创建客户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建客户失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 更新客户
export async function updateCustomer(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    const updateData: CustomerUpdateInput = req.body;
    
    // 检查客户是否存在
    const existingCustomer = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM customers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingCustomer) {
      res.status(404).json({
        success: false,
        message: '客户不存在'
      });
      return;
    }
    
    // 如果更新编码，检查是否与其他客户重复
    if (updateData.code) {
      const duplicateCustomer = await new Promise<any>((resolve, reject) => {
        db.get(
          'SELECT id FROM customers WHERE code = ? AND id != ? AND status != ?',
          [updateData.code, id, 'deleted'],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      
      if (duplicateCustomer) {
        res.status(400).json({
          success: false,
          message: '客户编码已存在'
        });
        return;
      }
    }
    
    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (updateData.code !== undefined) {
      updateFields.push('code = ?');
      updateValues.push(updateData.code);
    }
    if (updateData.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updateData.name);
    }
    if (updateData.contact_person !== undefined) {
      updateFields.push('contact_person = ?');
      updateValues.push(updateData.contact_person);
    }
    if (updateData.phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(updateData.phone);
    }
    if (updateData.address !== undefined) {
      updateFields.push('address = ?');
      updateValues.push(updateData.address);
    }
    if (updateData.credit_limit !== undefined) {
      updateFields.push('credit_limit = ?');
      updateValues.push(updateData.credit_limit);
    }
    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(updateData.status);
    }
    
    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
      return;
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateQuery = `UPDATE customers SET ${updateFields.join(', ')} WHERE id = ?`;
    
    await new Promise<void>((resolve, reject) => {
      db.run(updateQuery, updateValues, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    res.json({
      success: true,
      message: '更新客户成功'
    });
  } catch (error) {
    console.error('更新客户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新客户失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 删除客户（软删除）
export async function deleteCustomer(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    // 检查客户是否存在
    const existingCustomer = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM customers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingCustomer) {
      res.status(404).json({
        success: false,
        message: '客户不存在'
      });
      return;
    }
    
    // 软删除客户
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE customers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['deleted', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '删除客户成功'
    });
  } catch (error) {
    console.error('删除客户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除客户失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}
