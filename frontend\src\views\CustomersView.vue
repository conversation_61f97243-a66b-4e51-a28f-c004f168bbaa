<template>
  <div class="customers-view">
    <div class="page-header">
      <h1>客户管理</h1>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增客户
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索客户编码、名称或联系人"
        style="width: 300px"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="customers"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <template #status="{ row }">
        <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
          {{ row.status === 'active' ? '启用' : '禁用' }}
        </el-tag>
      </template>
      
      <template #credit_limit="{ row }">
        ¥{{ row.credit_limit.toFixed(2) }}
      </template>
      
      <template #actions="{ row }">
        <el-button type="primary" size="small" @click="handleEdit(row)">
          编辑
        </el-button>
        <el-button type="danger" size="small" @click="handleDelete(row)">
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 新增/编辑对话框 -->
    <FormDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :loading="dialogLoading"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="客户编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入客户编码" />
        </el-form-item>
        
        <el-form-item label="客户名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入客户名称" />
        </el-form-item>
        
        <el-form-item label="联系人" prop="contact_person">
          <el-input v-model="formData.contact_person" placeholder="请输入联系人" />
        </el-form-item>
        
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入联系电话" />
        </el-form-item>
        
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="formData.address"
            type="textarea"
            :rows="3"
            placeholder="请输入地址"
          />
        </el-form-item>
        
        <el-form-item label="信用额度" prop="credit_limit">
          <el-input-number
            v-model="formData.credit_limit"
            :min="0"
            :precision="2"
            placeholder="请输入信用额度"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status" v-if="isEdit">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
      </el-form>
    </FormDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable.vue'
import FormDialog from '@/components/FormDialog.vue'
import { customerApi, type Customer, type CustomerForm } from '@/api/customers'

// 响应式数据
const customers = ref<Customer[]>([])
const loading = ref(false)
const searchQuery = ref('')
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const isEdit = ref(false)
const editingId = ref<number | null>(null)

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = reactive<CustomerForm>({
  code: '',
  name: '',
  contact_person: '',
  phone: '',
  address: '',
  credit_limit: 0,
  status: 'active'
})

// 表单引用
const formRef = ref()

// 表格列配置
const columns = [
  { prop: 'code', label: '客户编码', width: 120 },
  { prop: 'name', label: '客户名称', width: 150 },
  { prop: 'contact_person', label: '联系人', width: 100 },
  { prop: 'phone', label: '联系电话', width: 120 },
  { prop: 'address', label: '地址', minWidth: 200 },
  { prop: 'credit_limit', label: '信用额度', width: 120, slot: 'credit_limit' },
  { prop: 'status', label: '状态', width: 80, slot: 'status' },
  { prop: 'created_at', label: '创建时间', width: 160, formatter: (row: Customer) => new Date(row.created_at).toLocaleString() },
  { label: '操作', width: 150, slot: 'actions' }
]

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入客户编码', trigger: 'blur' },
    { min: 1, max: 50, message: '编码长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  credit_limit: [
    { type: 'number', min: 0, message: '信用额度不能小于0', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑客户' : '新增客户')

// 加载客户列表
const loadCustomers = async () => {
  try {
    loading.value = true
    const response = await customerApi.getCustomers({
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: searchQuery.value
    })
    
    customers.value = response.data
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('加载客户列表失败')
    console.error('加载客户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadCustomers()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  loadCustomers()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadCustomers()
}

// 新增客户
const handleAdd = () => {
  isEdit.value = false
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑客户
const handleEdit = (customer: Customer) => {
  isEdit.value = true
  editingId.value = customer.id
  Object.assign(formData, customer)
  dialogVisible.value = true
}

// 删除客户
const handleDelete = async (customer: Customer) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${customer.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await customerApi.deleteCustomer(customer.id)
    ElMessage.success('删除成功')
    loadCustomers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除客户失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    dialogLoading.value = true
    
    if (isEdit.value && editingId.value) {
      await customerApi.updateCustomer(editingId.value, formData)
      ElMessage.success('更新成功')
    } else {
      await customerApi.createCustomer(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadCustomers()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error('提交表单失败:', error)
  } finally {
    dialogLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    contact_person: '',
    phone: '',
    address: '',
    credit_limit: 0,
    status: 'active'
  })
  formRef.value?.clearValidate()
}

// 组件挂载时加载数据
onMounted(() => {
  loadCustomers()
})
</script>

<style scoped>
.customers-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
}
</style>
