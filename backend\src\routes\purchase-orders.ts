import express from 'express';
import { 
  getPurchaseOrders, 
  createPurchaseOrder, 
  updatePurchaseOrder, 
  deletePurchaseOrder, 
  getPurchaseOrderById,
  approvePurchaseOrder,
  createPurchaseReceipt
} from '../controllers/purchaseOrderController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/purchase-orders - 获取采购订单列表
router.get('/', getPurchaseOrders);

// GET /api/purchase-orders/:id - 获取单个采购订单详情
router.get('/:id', getPurchaseOrderById);

// POST /api/purchase-orders - 创建采购订单
router.post('/', createPurchaseOrder);

// PUT /api/purchase-orders/:id - 更新采购订单
router.put('/:id', updatePurchaseOrder);

// DELETE /api/purchase-orders/:id - 删除采购订单
router.delete('/:id', deletePurchaseOrder);

// POST /api/purchase-orders/:id/approve - 审核采购订单
router.post('/:id/approve', approvePurchaseOrder);

// POST /api/purchase-orders/receipts - 创建采购入库单
router.post('/receipts', createPurchaseReceipt);

export default router;
