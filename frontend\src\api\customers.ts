import api from './index'
import type { ApiResponse } from './auth'

// 客户相关类型定义
export interface Customer {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  credit_limit: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface CustomerForm {
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  credit_limit?: number
  status?: 'active' | 'inactive'
}

export interface CustomerQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive'
}

export interface CustomerListResponse {
  data: Customer[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 客户管理API
export const customerApi = {
  // 获取客户列表
  async getCustomers(params: CustomerQuery = {}): Promise<CustomerListResponse> {
    const response = await api.get<ApiResponse<CustomerListResponse>>('/customers', { params })
    return response.data
  },

  // 获取单个客户详情
  async getCustomerById(id: number): Promise<Customer> {
    const response = await api.get<ApiResponse<Customer>>(`/customers/${id}`)
    return response.data
  },

  // 创建客户
  async createCustomer(data: CustomerForm): Promise<{ id: number }> {
    const response = await api.post<ApiResponse<{ id: number }>>('/customers', data)
    return response.data
  },

  // 更新客户
  async updateCustomer(id: number, data: CustomerForm): Promise<void> {
    await api.put(`/customers/${id}`, data)
  },

  // 删除客户
  async deleteCustomer(id: number): Promise<void> {
    await api.delete(`/customers/${id}`)
  }
}
