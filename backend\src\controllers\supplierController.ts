import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import type { 
  Supplier, 
  SupplierCreateInput, 
  SupplierUpdateInput, 
  PaginatedResponse 
} from '../types';

// 获取供应商列表
export async function getSuppliers(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { page = 1, limit = 10, search = '' } = req.query;
    
    const offset = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    let whereClause = "WHERE status != 'deleted'";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM suppliers ${whereClause}`;
    const countResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT * FROM suppliers 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...params, Number(limit), offset];
    
    const suppliers = await new Promise<Supplier[]>((resolve, reject) => {
      db.all(dataQuery, dataParams, (err, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows as Supplier[]);
      });
    });
    
    const response: PaginatedResponse<Supplier> = {
      data: suppliers,
      total: countResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(countResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取供应商列表成功',
      data: response
    });
  } catch (error) {
    console.error('获取供应商列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取供应商列表失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 获取单个供应商详情
export async function getSupplierById(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    const supplier = await new Promise<Supplier | undefined>((resolve, reject) => {
      db.get(
        'SELECT * FROM suppliers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row as Supplier);
        }
      );
    });
    
    if (!supplier) {
      res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
      return;
    }
    
    res.json({
      success: true,
      message: '获取供应商详情成功',
      data: supplier
    });
  } catch (error) {
    console.error('获取供应商详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取供应商详情失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 创建供应商
export async function createSupplier(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const supplierData: SupplierCreateInput = req.body;
    
    // 验证必填字段
    if (!supplierData.code || !supplierData.name) {
      res.status(400).json({
        success: false,
        message: '供应商编码和名称为必填项'
      });
      return;
    }
    
    // 检查编码是否已存在
    const existingSupplier = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM suppliers WHERE code = ? AND status != ?',
        [supplierData.code, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (existingSupplier) {
      res.status(400).json({
        success: false,
        message: '供应商编码已存在'
      });
      return;
    }
    
    // 插入新供应商
    const result = await new Promise<{ lastID: number }>((resolve, reject) => {
      db.run(
        `INSERT INTO suppliers (code, name, contact_person, phone, address, payment_terms, status, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          supplierData.code,
          supplierData.name,
          supplierData.contact_person || null,
          supplierData.phone || null,
          supplierData.address || null,
          supplierData.payment_terms || null
        ],
        function(err) {
          if (err) reject(err);
          else resolve({ lastID: this.lastID });
        }
      );
    });
    
    res.status(201).json({
      success: true,
      message: '创建供应商成功',
      data: { id: result.lastID }
    });
  } catch (error) {
    console.error('创建供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '创建供应商失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 更新供应商
export async function updateSupplier(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    const updateData: SupplierUpdateInput = req.body;
    
    // 检查供应商是否存在
    const existingSupplier = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM suppliers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingSupplier) {
      res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
      return;
    }
    
    // 如果更新编码，检查是否与其他供应商重复
    if (updateData.code) {
      const duplicateSupplier = await new Promise<any>((resolve, reject) => {
        db.get(
          'SELECT id FROM suppliers WHERE code = ? AND id != ? AND status != ?',
          [updateData.code, id, 'deleted'],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      
      if (duplicateSupplier) {
        res.status(400).json({
          success: false,
          message: '供应商编码已存在'
        });
        return;
      }
    }
    
    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (updateData.code !== undefined) {
      updateFields.push('code = ?');
      updateValues.push(updateData.code);
    }
    if (updateData.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(updateData.name);
    }
    if (updateData.contact_person !== undefined) {
      updateFields.push('contact_person = ?');
      updateValues.push(updateData.contact_person);
    }
    if (updateData.phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(updateData.phone);
    }
    if (updateData.address !== undefined) {
      updateFields.push('address = ?');
      updateValues.push(updateData.address);
    }
    if (updateData.payment_terms !== undefined) {
      updateFields.push('payment_terms = ?');
      updateValues.push(updateData.payment_terms);
    }
    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(updateData.status);
    }
    
    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
      return;
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateQuery = `UPDATE suppliers SET ${updateFields.join(', ')} WHERE id = ?`;
    
    await new Promise<void>((resolve, reject) => {
      db.run(updateQuery, updateValues, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    res.json({
      success: true,
      message: '更新供应商成功'
    });
  } catch (error) {
    console.error('更新供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '更新供应商失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 删除供应商（软删除）
export async function deleteSupplier(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    // 检查供应商是否存在
    const existingSupplier = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM suppliers WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingSupplier) {
      res.status(404).json({
        success: false,
        message: '供应商不存在'
      });
      return;
    }
    
    // 软删除供应商
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE suppliers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['deleted', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '删除供应商成功'
    });
  } catch (error) {
    console.error('删除供应商失败:', error);
    res.status(500).json({
      success: false,
      message: '删除供应商失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}
