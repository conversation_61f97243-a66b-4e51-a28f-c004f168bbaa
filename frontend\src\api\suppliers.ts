import api from './index'
import type { ApiResponse } from './auth'

// 供应商相关类型定义
export interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  payment_terms?: string
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface SupplierForm {
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  payment_terms?: string
  status?: 'active' | 'inactive'
}

export interface SupplierQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive'
}

export interface SupplierListResponse {
  data: Supplier[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 供应商管理API
export const supplierApi = {
  // 获取供应商列表
  async getSuppliers(params: SupplierQuery = {}): Promise<SupplierListResponse> {
    const response = await api.get<ApiResponse<SupplierListResponse>>('/suppliers', { params })
    return response.data
  },

  // 获取单个供应商详情
  async getSupplierById(id: number): Promise<Supplier> {
    const response = await api.get<ApiResponse<Supplier>>(`/suppliers/${id}`)
    return response.data
  },

  // 创建供应商
  async createSupplier(data: SupplierForm): Promise<{ id: number }> {
    const response = await api.post<ApiResponse<{ id: number }>>('/suppliers', data)
    return response.data
  },

  // 更新供应商
  async updateSupplier(id: number, data: SupplierForm): Promise<void> {
    await api.put(`/suppliers/${id}`, data)
  },

  // 删除供应商
  async deleteSupplier(id: number): Promise<void> {
    await api.delete(`/suppliers/${id}`)
  }
}
