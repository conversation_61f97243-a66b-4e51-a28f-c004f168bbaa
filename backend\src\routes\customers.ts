import express from 'express';
import { 
  getCustomers, 
  createCustomer, 
  updateCustomer, 
  deleteCustomer, 
  getCustomerById 
} from '../controllers/customerController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/customers - 获取客户列表
router.get('/', getCustomers);

// GET /api/customers/:id - 获取单个客户详情
router.get('/:id', getCustomerById);

// POST /api/customers - 创建客户
router.post('/', createCustomer);

// PUT /api/customers/:id - 更新客户
router.put('/:id', updateCustomer);

// DELETE /api/customers/:id - 删除客户
router.delete('/:id', deleteCustomer);

export default router;
