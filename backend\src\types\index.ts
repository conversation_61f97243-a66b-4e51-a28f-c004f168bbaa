// 用户相关类型
export interface User {
  id: number;
  username: string;
  password: string;
  email?: string;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export interface UserCreateInput {
  username: string;
  password: string;
  email?: string;
  role?: 'admin' | 'user';
}

export interface UserLoginInput {
  username: string;
  password: string;
}

// 原材料相关类型
export interface Material {
  id: number;
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price: number;
  stock_min: number;
  stock_max: number;
  current_stock: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface MaterialCreateInput {
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
}

export interface MaterialUpdateInput {
  code?: string;
  name?: string;
  specification?: string;
  unit?: string;
  cost_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
  status?: 'active' | 'inactive';
}

// 成品相关类型
export interface Product {
  id: number;
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price: number;
  sale_price: number;
  stock_min: number;
  stock_max: number;
  current_stock: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface ProductCreateInput {
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price?: number;
  sale_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
}

export interface ProductUpdateInput {
  code?: string;
  name?: string;
  specification?: string;
  unit?: string;
  cost_price?: number;
  sale_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
  status?: 'active' | 'inactive';
}

// 供应商相关类型
export interface Supplier {
  id: number;
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  payment_terms?: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface SupplierCreateInput {
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  payment_terms?: string;
}

export interface SupplierUpdateInput {
  code?: string;
  name?: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  payment_terms?: string;
  status?: 'active' | 'inactive';
}

// 客户相关类型
export interface Customer {
  id: number;
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface CustomerCreateInput {
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit?: number;
}

export interface CustomerUpdateInput {
  code?: string;
  name?: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit?: number;
  status?: 'active' | 'inactive';
}

// 采购订单相关类型
export interface PurchaseOrder {
  id: number;
  order_no: string;
  supplier_id: number;
  order_date: string;
  status: 'pending' | 'approved' | 'completed' | 'cancelled';
  total_amount: number;
  remark?: string;
  created_at: string;
  updated_at: string;
  supplier?: Supplier;
  items?: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  id: number;
  purchase_order_id: number;
  material_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  material?: Material;
}

export interface PurchaseOrderCreateInput {
  order_no: string;
  supplier_id: number;
  order_date: string;
  remark?: string;
  items: {
    material_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface PurchaseOrderUpdateInput {
  order_no?: string;
  supplier_id?: number;
  order_date?: string;
  status?: 'pending' | 'approved' | 'completed' | 'cancelled';
  remark?: string;
}

// 采购入库单相关类型
export interface PurchaseReceipt {
  id: number;
  receipt_no: string;
  purchase_order_id: number;
  receipt_date: string;
  status: 'completed' | 'cancelled';
  remark?: string;
  created_at: string;
  updated_at: string;
  purchase_order?: PurchaseOrder;
}

export interface PurchaseReceiptCreateInput {
  receipt_no: string;
  purchase_order_id: number;
  receipt_date: string;
  remark?: string;
}

// 生产计划相关类型
export interface ProductionPlan {
  id: number;
  plan_no: string;
  product_id: number;
  planned_quantity: number;
  plan_date: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  remark?: string;
  created_at: string;
  updated_at: string;
  product?: Product;
}

export interface ProductionPlanCreateInput {
  plan_no: string;
  product_id: number;
  planned_quantity: number;
  plan_date: string;
  remark?: string;
}

export interface ProductionPlanUpdateInput {
  plan_no?: string;
  product_id?: number;
  planned_quantity?: number;
  plan_date?: string;
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  remark?: string;
}

// 生产完工相关类型
export interface ProductionCompletion {
  id: number;
  completion_no: string;
  production_plan_id: number;
  completion_date: string;
  actual_quantity: number;
  status: 'completed' | 'cancelled';
  remark?: string;
  created_at: string;
  updated_at: string;
  production_plan?: ProductionPlan;
}

export interface ProductionCompletionCreateInput {
  completion_no: string;
  production_plan_id: number;
  completion_date: string;
  actual_quantity: number;
  remark?: string;
}

// 销售订单相关类型
export interface SalesOrder {
  id: number;
  order_no: string;
  customer_id: number;
  order_date: string;
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  total_amount: number;
  remark?: string;
  created_at: string;
  updated_at: string;
  customer?: Customer;
  items?: SalesOrderItem[];
}

export interface SalesOrderItem {
  id: number;
  sales_order_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  product?: Product;
}

export interface SalesOrderCreateInput {
  order_no: string;
  customer_id: number;
  order_date: string;
  remark?: string;
  items: {
    product_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface SalesOrderUpdateInput {
  order_no?: string;
  customer_id?: number;
  order_date?: string;
  status?: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  remark?: string;
}

// 销售出库单相关类型
export interface SalesDelivery {
  id: number;
  delivery_no: string;
  sales_order_id: number;
  delivery_date: string;
  status: 'completed' | 'cancelled';
  remark?: string;
  created_at: string;
  updated_at: string;
  sales_order?: SalesOrder;
}

export interface SalesDeliveryCreateInput {
  delivery_no: string;
  sales_order_id: number;
  delivery_date: string;
  remark?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 分页相关类型
export interface PaginationQuery {
  page?: number;
  limit?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
