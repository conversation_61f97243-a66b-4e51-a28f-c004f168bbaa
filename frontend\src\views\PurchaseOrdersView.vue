<template>
  <div class="purchase-orders-view">
    <div class="page-header">
      <h1>采购订单管理</h1>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增采购订单
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="搜索订单号或供应商"
        style="width: 300px; margin-right: 10px"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      
      <el-select
        v-model="statusFilter"
        placeholder="选择状态"
        style="width: 150px"
        clearable
        @change="handleSearch"
      >
        <el-option label="待审核" value="pending" />
        <el-option label="已审核" value="approved" />
        <el-option label="已完成" value="completed" />
        <el-option label="已取消" value="cancelled" />
      </el-select>
    </div>

    <!-- 数据表格 -->
    <DataTable
      :data="purchaseOrders"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <template #total_amount="{ row }">
        ¥{{ row.total_amount.toFixed(2) }}
      </template>
      
      <template #actions="{ row }">
        <el-button type="primary" size="small" @click="handleView(row)">
          查看
        </el-button>
        <el-button 
          v-if="row.status === 'pending'" 
          type="success" 
          size="small" 
          @click="handleApprove(row)"
        >
          审核
        </el-button>
        <el-button 
          v-if="row.status === 'approved'" 
          type="warning" 
          size="small" 
          @click="handleReceipt(row)"
        >
          入库
        </el-button>
        <el-button 
          v-if="row.status === 'pending'" 
          type="danger" 
          size="small" 
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 新增/编辑对话框 -->
    <FormDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :loading="dialogLoading"
      width="800px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单号" prop="order_no">
              <el-input v-model="formData.order_no" placeholder="请输入订单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select
                v-model="formData.supplier_id"
                placeholder="请选择供应商"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单日期" prop="order_date">
              <el-date-picker
                v-model="formData.order_date"
                type="date"
                placeholder="请选择订单日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="formData.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 订单明细 -->
        <el-form-item label="订单明细" required>
          <div style="width: 100%">
            <div style="margin-bottom: 10px">
              <el-button type="primary" size="small" @click="addOrderItem">
                添加明细
              </el-button>
            </div>
            
            <el-table :data="formData.items" border style="width: 100%">
              <el-table-column label="原材料" min-width="150">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.material_id"
                    placeholder="请选择原材料"
                    filterable
                    style="width: 100%"
                    @change="onMaterialChange(row, $index)"
                  >
                    <el-option
                      v-for="material in materials"
                      :key="material.id"
                      :label="`${material.name} (${material.code})`"
                      :value="material.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              
              <el-table-column label="数量" width="120">
                <template #default="{ row, $index }">
                  <el-input-number
                    v-model="row.quantity"
                    :min="1"
                    style="width: 100%"
                    @change="calculateItemTotal(row, $index)"
                  />
                </template>
              </el-table-column>
              
              <el-table-column label="单价" width="120">
                <template #default="{ row, $index }">
                  <el-input-number
                    v-model="row.unit_price"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                    @change="calculateItemTotal(row, $index)"
                  />
                </template>
              </el-table-column>
              
              <el-table-column label="小计" width="120">
                <template #default="{ row }">
                  ¥{{ (row.quantity * row.unit_price).toFixed(2) }}
                </template>
              </el-table-column>
              
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeOrderItem($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div style="margin-top: 10px; text-align: right">
              <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </FormDialog>

    <!-- 入库对话框 -->
    <FormDialog
      v-model="receiptDialogVisible"
      title="创建采购入库单"
      :loading="receiptDialogLoading"
      @confirm="handleReceiptSubmit"
      @cancel="receiptDialogVisible = false"
    >
      <el-form
        ref="receiptFormRef"
        :model="receiptFormData"
        :rules="receiptFormRules"
        label-width="100px"
      >
        <el-form-item label="入库单号" prop="receipt_no">
          <el-input v-model="receiptFormData.receipt_no" placeholder="请输入入库单号" />
        </el-form-item>
        
        <el-form-item label="入库日期" prop="receipt_date">
          <el-date-picker
            v-model="receiptFormData.receipt_date"
            type="date"
            placeholder="请选择入库日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input v-model="receiptFormData.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
    </FormDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable.vue'
import FormDialog from '@/components/FormDialog.vue'
import { purchaseOrderApi, type PurchaseOrder, type PurchaseOrderForm } from '@/api/purchase-orders'
import { supplierApi, type Supplier } from '@/api/suppliers'
import { materialApi, type Material } from '@/api/materials'

// 响应式数据
const purchaseOrders = ref<PurchaseOrder[]>([])
const suppliers = ref<Supplier[]>([])
const materials = ref<Material[]>([])
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const receiptDialogVisible = ref(false)
const receiptDialogLoading = ref(false)
const isEdit = ref(false)
const editingId = ref<number | null>(null)

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = reactive<PurchaseOrderForm>({
  order_no: '',
  supplier_id: 0,
  order_date: '',
  remark: '',
  items: []
})

// 入库表单数据
const receiptFormData = reactive({
  purchase_order_id: 0,
  receipt_no: '',
  receipt_date: '',
  remark: ''
})

// 表单引用
const formRef = ref()
const receiptFormRef = ref()

// 表格列配置
const columns = [
  { prop: 'order_no', label: '订单号', width: 150 },
  { prop: 'supplier.name', label: '供应商', width: 150 },
  { prop: 'order_date', label: '订单日期', width: 120 },
  { prop: 'status', label: '状态', width: 100, slot: 'status' },
  { prop: 'total_amount', label: '总金额', width: 120, slot: 'total_amount' },
  { prop: 'remark', label: '备注', minWidth: 150 },
  { prop: 'created_at', label: '创建时间', width: 160, formatter: (row: PurchaseOrder) => new Date(row.created_at).toLocaleString() },
  { label: '操作', width: 200, slot: 'actions' }
]

// 表单验证规则
const formRules = {
  order_no: [
    { required: true, message: '请输入订单号', trigger: 'blur' }
  ],
  supplier_id: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  order_date: [
    { required: true, message: '请选择订单日期', trigger: 'change' }
  ]
}

const receiptFormRules = {
  receipt_no: [
    { required: true, message: '请输入入库单号', trigger: 'blur' }
  ],
  receipt_date: [
    { required: true, message: '请选择入库日期', trigger: 'change' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑采购订单' : '新增采购订单')

const totalAmount = computed(() => {
  return formData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0)
})

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    approved: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待审核',
    approved: '已审核',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 加载数据方法
const loadPurchaseOrders = async () => {
  try {
    loading.value = true
    const response = await purchaseOrderApi.getPurchaseOrders({
      page: pagination.page,
      pageSize: pagination.pageSize,
      search: searchQuery.value,
      status: statusFilter.value || undefined
    })
    
    purchaseOrders.value = response.data
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('加载采购订单列表失败')
    console.error('加载采购订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadSuppliers = async () => {
  try {
    const response = await supplierApi.getSuppliers({ pageSize: 1000 })
    suppliers.value = response.data.filter(s => s.status === 'active')
  } catch (error) {
    console.error('加载供应商列表失败:', error)
  }
}

const loadMaterials = async () => {
  try {
    const response = await materialApi.getMaterials({ pageSize: 1000 })
    materials.value = response.data.filter(m => m.status === 'active')
  } catch (error) {
    console.error('加载原材料列表失败:', error)
  }
}

// 搜索和分页处理
const handleSearch = () => {
  pagination.page = 1
  loadPurchaseOrders()
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadPurchaseOrders()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadPurchaseOrders()
}

// 订单明细操作
const addOrderItem = () => {
  formData.items.push({
    material_id: 0,
    quantity: 1,
    unit_price: 0
  })
}

const removeOrderItem = (index: number) => {
  formData.items.splice(index, 1)
}

const onMaterialChange = (row: any, index: number) => {
  const material = materials.value.find(m => m.id === row.material_id)
  if (material) {
    row.unit_price = material.cost_price
    calculateItemTotal(row, index)
  }
}

const calculateItemTotal = (row: any, index: number) => {
  // 触发响应式更新
  formData.items[index] = { ...row }
}

// 操作方法
const handleAdd = () => {
  isEdit.value = false
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

const handleView = (order: PurchaseOrder) => {
  // TODO: 实现查看详情功能
  ElMessage.info('查看功能待实现')
}

const handleApprove = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要审核采购订单 "${order.order_no}" 吗？`,
      '确认审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await purchaseOrderApi.approvePurchaseOrder(order.id)
    ElMessage.success('审核成功')
    loadPurchaseOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核失败')
      console.error('审核采购订单失败:', error)
    }
  }
}

const handleReceipt = (order: PurchaseOrder) => {
  receiptFormData.purchase_order_id = order.id
  receiptFormData.receipt_no = `REC${Date.now()}`
  receiptFormData.receipt_date = new Date().toISOString().split('T')[0]
  receiptFormData.remark = ''
  receiptDialogVisible.value = true
}

const handleDelete = async (order: PurchaseOrder) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除采购订单 "${order.order_no}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await purchaseOrderApi.deletePurchaseOrder(order.id)
    ElMessage.success('删除成功')
    loadPurchaseOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除采购订单失败:', error)
    }
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    if (formData.items.length === 0) {
      ElMessage.error('请添加至少一个订单明细')
      return
    }
    
    dialogLoading.value = true
    
    await purchaseOrderApi.createPurchaseOrder(formData)
    ElMessage.success('创建成功')
    
    dialogVisible.value = false
    loadPurchaseOrders()
  } catch (error) {
    ElMessage.error('创建失败')
    console.error('提交表单失败:', error)
  } finally {
    dialogLoading.value = false
  }
}

const handleReceiptSubmit = async () => {
  try {
    await receiptFormRef.value?.validate()
    receiptDialogLoading.value = true
    
    await purchaseOrderApi.createPurchaseReceipt(receiptFormData)
    ElMessage.success('入库成功')
    
    receiptDialogVisible.value = false
    loadPurchaseOrders()
  } catch (error) {
    ElMessage.error('入库失败')
    console.error('创建入库单失败:', error)
  } finally {
    receiptDialogLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    order_no: '',
    supplier_id: 0,
    order_date: '',
    remark: '',
    items: []
  })
  formRef.value?.clearValidate()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPurchaseOrders()
  loadSuppliers()
  loadMaterials()
})
</script>

<style scoped>
.purchase-orders-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}
</style>
