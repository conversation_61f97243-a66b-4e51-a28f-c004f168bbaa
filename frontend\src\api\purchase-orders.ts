import api from './index'
import type { ApiResponse } from './auth'
import type { Supplier } from './suppliers'
import type { Material } from './materials'

// 采购订单相关类型定义
export interface PurchaseOrder {
  id: number
  order_no: string
  supplier_id: number
  order_date: string
  status: 'pending' | 'approved' | 'completed' | 'cancelled'
  total_amount: number
  remark?: string
  created_at: string
  updated_at: string
  supplier?: Supplier
  items?: PurchaseOrderItem[]
}

export interface PurchaseOrderItem {
  id: number
  purchase_order_id: number
  material_id: number
  quantity: number
  unit_price: number
  total_price: number
  created_at: string
  material?: Material
}

export interface PurchaseOrderForm {
  order_no: string
  supplier_id: number
  order_date: string
  remark?: string
  items: {
    material_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface PurchaseOrderQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: 'pending' | 'approved' | 'completed' | 'cancelled'
}

export interface PurchaseOrderListResponse {
  data: PurchaseOrder[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface PurchaseReceiptForm {
  purchase_order_id: number
  receipt_no: string
  receipt_date: string
  remark?: string
}

// 采购订单管理API
export const purchaseOrderApi = {
  // 获取采购订单列表
  async getPurchaseOrders(params: PurchaseOrderQuery = {}): Promise<PurchaseOrderListResponse> {
    const response = await api.get<ApiResponse<PurchaseOrderListResponse>>('/purchase-orders', { params })
    return response.data
  },

  // 获取单个采购订单详情
  async getPurchaseOrderById(id: number): Promise<PurchaseOrder> {
    const response = await api.get<ApiResponse<PurchaseOrder>>(`/purchase-orders/${id}`)
    return response.data
  },

  // 创建采购订单
  async createPurchaseOrder(data: PurchaseOrderForm): Promise<void> {
    await api.post('/purchase-orders', data)
  },

  // 更新采购订单
  async updatePurchaseOrder(id: number, data: { status?: string; remark?: string }): Promise<void> {
    await api.put(`/purchase-orders/${id}`, data)
  },

  // 删除采购订单
  async deletePurchaseOrder(id: number): Promise<void> {
    await api.delete(`/purchase-orders/${id}`)
  },

  // 审核采购订单
  async approvePurchaseOrder(id: number): Promise<void> {
    await api.post(`/purchase-orders/${id}/approve`)
  },

  // 创建采购入库单
  async createPurchaseReceipt(data: PurchaseReceiptForm): Promise<void> {
    await api.post('/purchase-orders/receipts', data)
  }
}
