import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import type { 
  PurchaseOrder, 
  PurchaseOrderCreateInput, 
  PurchaseOrderUpdateInput, 
  PaginatedResponse 
} from '../types';

// 获取采购订单列表
export async function getPurchaseOrders(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    
    const offset = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    let whereClause = "WHERE po.status != 'deleted'";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (po.order_no LIKE ? OR s.name LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }
    
    if (status) {
      whereClause += " AND po.status = ?";
      params.push(status);
    }
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM purchase_orders po 
      LEFT JOIN suppliers s ON po.supplier_id = s.id 
      ${whereClause}
    `;
    const countResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row: any) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT po.*, s.name as supplier_name, s.code as supplier_code
      FROM purchase_orders po 
      LEFT JOIN suppliers s ON po.supplier_id = s.id 
      ${whereClause} 
      ORDER BY po.created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...params, Number(limit), offset];
    
    const purchaseOrders = await new Promise<any[]>((resolve, reject) => {
      db.all(dataQuery, dataParams, (err, rows: any[]) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
    
    const response: PaginatedResponse<PurchaseOrder> = {
      data: purchaseOrders.map(row => ({
        ...row,
        supplier: row.supplier_name ? {
          id: row.supplier_id,
          name: row.supplier_name,
          code: row.supplier_code
        } : undefined
      })),
      total: countResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(countResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取采购订单列表成功',
      data: response
    });
  } catch (error) {
    console.error('获取采购订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采购订单列表失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 获取单个采购订单详情
export async function getPurchaseOrderById(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    // 获取采购订单基本信息
    const purchaseOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        `SELECT po.*, s.name as supplier_name, s.code as supplier_code
         FROM purchase_orders po 
         LEFT JOIN suppliers s ON po.supplier_id = s.id 
         WHERE po.id = ? AND po.status != 'deleted'`,
        [id],
        (err, row: any) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!purchaseOrder) {
      res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
      return;
    }
    
    // 获取采购订单明细
    const items = await new Promise<any[]>((resolve, reject) => {
      db.all(
        `SELECT poi.*, m.name as material_name, m.code as material_code, m.unit as material_unit
         FROM purchase_order_items poi 
         LEFT JOIN materials m ON poi.material_id = m.id 
         WHERE poi.purchase_order_id = ?`,
        [id],
        (err, rows: any[]) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    
    const result = {
      ...purchaseOrder,
      supplier: purchaseOrder.supplier_name ? {
        id: purchaseOrder.supplier_id,
        name: purchaseOrder.supplier_name,
        code: purchaseOrder.supplier_code
      } : undefined,
      items: items.map(item => ({
        ...item,
        material: {
          id: item.material_id,
          name: item.material_name,
          code: item.material_code,
          unit: item.material_unit
        }
      }))
    };
    
    res.json({
      success: true,
      message: '获取采购订单详情成功',
      data: result
    });
  } catch (error) {
    console.error('获取采购订单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取采购订单详情失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 创建采购订单
export async function createPurchaseOrder(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const orderData: PurchaseOrderCreateInput = req.body;
    
    // 验证必填字段
    if (!orderData.order_no || !orderData.supplier_id || !orderData.items || orderData.items.length === 0) {
      res.status(400).json({
        success: false,
        message: '订单号、供应商和订单明细为必填项'
      });
      return;
    }
    
    // 检查订单号是否已存在
    const existingOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM purchase_orders WHERE order_no = ? AND status != ?',
        [orderData.order_no, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (existingOrder) {
      res.status(400).json({
        success: false,
        message: '采购订单号已存在'
      });
      return;
    }
    
    // 计算总金额
    const totalAmount = orderData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    
    // 开始事务
    await new Promise<void>((resolve, reject) => {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        // 插入采购订单
        db.run(
          `INSERT INTO purchase_orders (order_no, supplier_id, order_date, status, total_amount, remark, created_at, updated_at)
           VALUES (?, ?, ?, 'pending', ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          [orderData.order_no, orderData.supplier_id, orderData.order_date, totalAmount, orderData.remark || null],
          function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }
            
            const orderId = this.lastID;
            
            // 插入订单明细
            let itemsInserted = 0;
            const totalItems = orderData.items.length;
            
            orderData.items.forEach(item => {
              const itemTotalPrice = item.quantity * item.unit_price;
              db.run(
                `INSERT INTO purchase_order_items (purchase_order_id, material_id, quantity, unit_price, total_price, created_at)
                 VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                [orderId, item.material_id, item.quantity, item.unit_price, itemTotalPrice],
                (err) => {
                  if (err) {
                    db.run('ROLLBACK');
                    reject(err);
                    return;
                  }
                  
                  itemsInserted++;
                  if (itemsInserted === totalItems) {
                    db.run('COMMIT', (err) => {
                      if (err) {
                        reject(err);
                      } else {
                        resolve();
                      }
                    });
                  }
                }
              );
            });
          }
        );
      });
    });
    
    res.status(201).json({
      success: true,
      message: '创建采购订单成功'
    });
  } catch (error) {
    console.error('创建采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建采购订单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 更新采购订单状态
export async function updatePurchaseOrder(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    const updateData: PurchaseOrderUpdateInput = req.body;
    
    // 检查采购订单是否存在
    const existingOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id, status FROM purchase_orders WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingOrder) {
      res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
      return;
    }
    
    // 构建更新语句
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (updateData.status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(updateData.status);
    }
    if (updateData.remark !== undefined) {
      updateFields.push('remark = ?');
      updateValues.push(updateData.remark);
    }
    
    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
      return;
    }
    
    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    updateValues.push(id);
    
    const updateQuery = `UPDATE purchase_orders SET ${updateFields.join(', ')} WHERE id = ?`;
    
    await new Promise<void>((resolve, reject) => {
      db.run(updateQuery, updateValues, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    
    res.json({
      success: true,
      message: '更新采购订单成功'
    });
  } catch (error) {
    console.error('更新采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '更新采购订单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 删除采购订单（软删除）
export async function deletePurchaseOrder(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;
    
    // 检查采购订单是否存在
    const existingOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id, status FROM purchase_orders WHERE id = ? AND status != ?',
        [id, 'deleted'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    
    if (!existingOrder) {
      res.status(404).json({
        success: false,
        message: '采购订单不存在'
      });
      return;
    }
    
    // 只有待审核状态的订单才能删除
    if (existingOrder.status !== 'pending') {
      res.status(400).json({
        success: false,
        message: '只有待审核状态的采购订单才能删除'
      });
      return;
    }
    
    // 软删除采购订单
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE purchase_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['deleted', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
    res.json({
      success: true,
      message: '删除采购订单成功'
    });
  } catch (error) {
    console.error('删除采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '删除采购订单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 审核采购订单
export async function approvePurchaseOrder(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { id } = req.params;

    // 检查采购订单是否存在且状态为待审核
    const existingOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id, status FROM purchase_orders WHERE id = ? AND status = ?',
        [id, 'pending'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!existingOrder) {
      res.status(404).json({
        success: false,
        message: '采购订单不存在或状态不正确'
      });
      return;
    }

    // 更新订单状态为已审核
    await new Promise<void>((resolve, reject) => {
      db.run(
        'UPDATE purchase_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['approved', id],
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    res.json({
      success: true,
      message: '审核采购订单成功'
    });
  } catch (error) {
    console.error('审核采购订单失败:', error);
    res.status(500).json({
      success: false,
      message: '审核采购订单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}

// 创建采购入库单
export async function createPurchaseReceipt(req: Request, res: Response): Promise<void> {
  try {
    const db = getDatabase();
    const { purchase_order_id, receipt_no, receipt_date, remark } = req.body;

    // 验证必填字段
    if (!purchase_order_id || !receipt_no || !receipt_date) {
      res.status(400).json({
        success: false,
        message: '采购订单ID、入库单号和入库日期为必填项'
      });
      return;
    }

    // 检查采购订单是否存在且已审核
    const purchaseOrder = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id, status FROM purchase_orders WHERE id = ? AND status = ?',
        [purchase_order_id, 'approved'],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!purchaseOrder) {
      res.status(404).json({
        success: false,
        message: '采购订单不存在或未审核'
      });
      return;
    }

    // 检查入库单号是否已存在
    const existingReceipt = await new Promise<any>((resolve, reject) => {
      db.get(
        'SELECT id FROM purchase_receipts WHERE receipt_no = ?',
        [receipt_no],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingReceipt) {
      res.status(400).json({
        success: false,
        message: '入库单号已存在'
      });
      return;
    }

    // 获取采购订单明细
    const orderItems = await new Promise<any[]>((resolve, reject) => {
      db.all(
        'SELECT material_id, quantity FROM purchase_order_items WHERE purchase_order_id = ?',
        [purchase_order_id],
        (err, rows: any[]) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // 开始事务：创建入库单并更新库存
    await new Promise<void>((resolve, reject) => {
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // 创建入库单
        db.run(
          `INSERT INTO purchase_receipts (receipt_no, purchase_order_id, receipt_date, status, remark, created_at, updated_at)
           VALUES (?, ?, ?, 'completed', ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          [receipt_no, purchase_order_id, receipt_date, remark || null],
          function(err) {
            if (err) {
              db.run('ROLLBACK');
              reject(err);
              return;
            }

            // 更新原材料库存
            let itemsUpdated = 0;
            const totalItems = orderItems.length;

            if (totalItems === 0) {
              // 更新采购订单状态为已完成
              db.run(
                'UPDATE purchase_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                ['completed', purchase_order_id],
                (err) => {
                  if (err) {
                    db.run('ROLLBACK');
                    reject(err);
                  } else {
                    db.run('COMMIT', (err) => {
                      if (err) reject(err);
                      else resolve();
                    });
                  }
                }
              );
              return;
            }

            orderItems.forEach(item => {
              db.run(
                'UPDATE materials SET current_stock = current_stock + ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [item.quantity, item.material_id],
                (err) => {
                  if (err) {
                    db.run('ROLLBACK');
                    reject(err);
                    return;
                  }

                  itemsUpdated++;
                  if (itemsUpdated === totalItems) {
                    // 更新采购订单状态为已完成
                    db.run(
                      'UPDATE purchase_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                      ['completed', purchase_order_id],
                      (err) => {
                        if (err) {
                          db.run('ROLLBACK');
                          reject(err);
                        } else {
                          db.run('COMMIT', (err) => {
                            if (err) reject(err);
                            else resolve();
                          });
                        }
                      }
                    );
                  }
                }
              );
            });
          }
        );
      });
    });

    res.status(201).json({
      success: true,
      message: '创建采购入库单成功，库存已更新'
    });
  } catch (error) {
    console.error('创建采购入库单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建采购入库单失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
}
